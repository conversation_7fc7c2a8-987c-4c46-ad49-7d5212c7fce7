"use client";

import React, { useState, useEffect } from 'react';
import { loadStripe, Stripe } from '@stripe/stripe-js';
import { getPublishableKeyByCurrency } from "@/lib/stripeClient";
import {
  Elements,
  PaymentElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';


interface ExistingPaymentFormProps {
  paymentIntentId: string;
  amount: number;
  currency: string;
  productName: string;
  isEscrow?: boolean;
  onSuccess?: (paymentIntent: any) => void;
  onError?: (error: string) => void;
}

function CheckoutForm({ onSuccess, onError }: { 
  onSuccess?: (paymentIntent: any) => void;
  onError?: (error: string) => void;
}) {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);

    // Submit the form to validate all fields
    const { error: submitError } = await elements.submit();
    if (submitError) {
      setMessage(submitError.message || "Please check your payment details");
      onError?.(submitError.message || "Please check your payment details");
      setIsLoading(false);
      return;
    }

    // Confirm payment without redirect
    const { error, paymentIntent } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: `${window.location.origin}/payment-success`,
      },
      redirect: "if_required"
    });

    if (error) {
      if (error.type === "card_error" || error.type === "validation_error") {
        setMessage(error.message || "An error occurred");
        onError?.(error.message || "An error occurred");
      } else {
        setMessage("An unexpected error occurred.");
        onError?.("An unexpected error occurred.");
      }
    } else if (paymentIntent && paymentIntent.status === "succeeded") {
      setMessage("Payment succeeded!");
      onSuccess?.(paymentIntent);
    }

    setIsLoading(false);
  };

  const paymentElementOptions = {
    layout: "tabs" as const,
  };

  return (
    <form id="payment-form" onSubmit={handleSubmit}>
      <PaymentElement id="payment-element" options={paymentElementOptions} />
      <button 
        disabled={isLoading || !stripe || !elements} 
        id="submit"
        className="w-full mt-6 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-3 px-4 rounded transition-colors"
      >
        <span id="button-text">
          {isLoading ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Processing...
            </div>
          ) : (
            "Pay now"
          )}
        </span>
      </button>
      {message && (
        <div className={`mt-4 p-3 rounded ${
          message.includes("succeeded") 
            ? "bg-green-100 text-green-700 border border-green-400" 
            : "bg-red-100 text-red-700 border border-red-400"
        }`}>
          {message}
        </div>
      )}
    </form>
  );
}

export default function ExistingPaymentForm({
  paymentIntentId,
  amount,
  currency,
  productName,
  isEscrow = false,
  onSuccess,
  onError
}: ExistingPaymentFormProps) {
  const [clientSecret, setClientSecret] = useState("");
  const [stripePromise, setStripePromise] = useState<Promise<Stripe | null> | null>(null);

  useEffect(() => {
    const pk = getPublishableKeyByCurrency(currency).publishableKey;
    if (!pk) {
      console.error('Stripe publishable key is not configured');
      return;
    }
    setStripePromise(loadStripe(pk));
  }, [currency]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getPaymentIntent = async () => {
      try {
        console.log('🔄 Retrieving existing payment intent...', paymentIntentId);
        
        const response = await fetch(`/api/payment-intent/${paymentIntentId}`, {
          method: "GET",
          headers: { "Content-Type": "application/json" },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('✅ Payment intent retrieved:', data);
        
        if (data.clientSecret) {
          setClientSecret(data.clientSecret);
          console.log('✅ Client secret set from existing payment intent');
        } else {
          throw new Error(data.error || "No client secret in existing payment intent");
        }
      } catch (err) {
        console.error('❌ Error retrieving payment intent:', err);
        const errorMessage = err instanceof Error ? err.message : "Unknown error";
        setError(errorMessage);
        onError?.(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    if (paymentIntentId) {
      getPaymentIntent();
    } else {
      setError("No payment intent ID provided");
      setLoading(false);
    }
  }, [paymentIntentId, onError]);

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center p-8 space-y-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="text-gray-600">Loading payment form...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 text-red-700 rounded-lg">
        <h3 className="font-semibold text-lg mb-2">Payment Error</h3>
        <p className="mb-4">{error}</p>
        <button 
          onClick={() => window.location.reload()} 
          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (!clientSecret) {
    return (
      <div className="p-6 bg-yellow-50 border border-yellow-200 text-yellow-700 rounded-lg">
        <p>Initializing payment form...</p>
      </div>
    );
  }

  const appearance = {
    theme: 'stripe' as const,
    variables: {
      colorPrimary: '#2563eb',
      colorBackground: '#ffffff',
      colorText: '#1f2937',
      colorDanger: '#dc2626',
      fontFamily: 'system-ui, sans-serif',
      spacingUnit: '4px',
      borderRadius: '6px',
    },
  };

  const options = {
    clientSecret,
    appearance,
  };

  return (
    <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow-sm border">
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2 text-gray-900">{productName}</h2>
        <div className="text-gray-600">
          <p className="text-lg font-medium">
            {currency.toUpperCase()} {(amount / 100).toFixed(2)}
          </p>
          {isEscrow && (
            <p className="text-sm text-blue-600 mt-1">
              🔒 Escrow Payment - Funds held securely
            </p>
          )}
        </div>
      </div>
      
      {stripePromise ? (
        <Elements options={options} stripe={stripePromise}>
        <CheckoutForm onSuccess={onSuccess} onError={onError} />
      </Elements>
      ) : (
        <div className="p-4 bg-yellow-50 border border-yellow-200 text-yellow-700 rounded">Initializing payment SDK...</div>
      )}
    </div>
  );
}
