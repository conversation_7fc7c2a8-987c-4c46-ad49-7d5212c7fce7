import { NextRequest, NextResponse } from 'next/server';
import { processPaymentSuccess, processPaymentFailure } from '@/services/postPaymentService';
import { getStripeInstance } from '@/lib/stripe';
import { UsersHandlerManager } from '@/lib/api-gateway-handlers/users-handler';
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('🎉 Payment success webhook received:', body);

    const {
      paymentIntentId,
      orderId,
      transactionId,
      amount,
      currency,
      isEscrow,
      userId,
      sellerId,
      userEmail,
      userName,
      sellerName
    } = body;

    // Validate required fields
    if (!paymentIntentId || !orderId || !userId || !sellerId) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: paymentIntentId, orderId, userId, sellerId'
      }, { status: 400 });
    }

    // Select Stripe platform strictly by seller's stored isUS flag
    let isUSPlatform = false;
    try {
      const resp = await UsersHandlerManager.getInstance().CheckSellerIsUS(sellerId);
      isUSPlatform = Boolean(resp?.isUS);
    } catch (e) {
      console.log('Could not resolve seller isUS from UsersHandler; defaulting to Global platform.');
    }
    let stripeClient = getStripeInstance(isUSPlatform);

    // Verify payment intent with Stripe
    let paymentIntent;
    try {
      paymentIntent = await stripeClient.paymentIntents.retrieve(paymentIntentId);
      console.log('✅ Payment intent verified:', {
        id: paymentIntent.id,
        status: paymentIntent.status,
        amount: paymentIntent.amount
      });
    } catch (stripeError) {
      console.error('❌ Failed to verify payment intent:', stripeError);
      const err: any = stripeError;
      if (err?.code === 'resource_missing' || err?.statusCode === 404) {
        try {
          const altClient = getStripeInstance(!isUSPlatform);
          paymentIntent = await altClient.paymentIntents.retrieve(paymentIntentId);
          stripeClient = altClient;
          console.log('✅ Payment intent found on alternate platform');
        } catch (altErr) {
          console.error('❌ Not found on alternate platform either:', altErr);
          return NextResponse.json({
            success: false,
            error: 'Invalid payment intent'
          }, { status: 400 });
        }
      } else {
        return NextResponse.json({
          success: false,
          error: 'Invalid payment intent'
        }, { status: 400 });
      }
    }

    // Check if payment actually succeeded or is authorized (for escrow)
    const validStatuses = ['succeeded', 'requires_capture'];
    if (!validStatuses.includes(paymentIntent.status)) {
      console.log('⚠️ Payment intent not in valid status:', paymentIntent.status);

      // Handle failed payment
      await processPaymentFailure({
        orderId,
        transactionId,
        error: `Payment status: ${paymentIntent.status}`,
        userId,
        sellerId,
        userName,
        sellerName
      });

      return NextResponse.json({
        success: false,
        error: `Payment not completed. Status: ${paymentIntent.status}`
      }, { status: 400 });
    }

    console.log('✅ Payment intent status is valid:', paymentIntent.status);

    // Get charge ID from payment intent
    // const charges = paymentIntent.charge?.data;
    // const chargeId = charges && charges.length > 0 ? charges[0].id : undefined;

    // Process successful payment
    console.log('🎯 Processing payment success with data:', {
      paymentIntentId,
      orderId,
      transactionId,
      amount: amount || paymentIntent.amount,
      currency: currency || paymentIntent.currency,
      isEscrow: isEscrow || false,
      userId,
      sellerId
    });

    // const result = await processPaymentSuccess({
    //   paymentIntentId,
    //   orderId,
    //   transactionId,
    //   amount: amount || paymentIntent.amount,
    //   currency: currency || paymentIntent.currency,
    //   isEscrow: isEscrow || false,
    //   userId,
    //   sellerId,
    //   userEmail,
    //   userName,
    //   sellerName,
    //   chargeId
    // });

    // if (!result.success) {
    //   console.error('❌ Failed to process payment success:', result.error);
    //   return NextResponse.json({
    //     success: false,
    //     error: result.error
    //   }, { status: 500 });
    // }

    console.log('✅ Payment success processed successfully');
    return NextResponse.json({
      success: true,
      message: 'Payment processed successfully',
      orderId,
      paymentIntentId,
      status: paymentIntent.status
    });

  } catch (error) {
    console.error('❌ Error in payment success endpoint:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
