import { NextRequest, NextResponse } from 'next/server';
import { UsersHandlerManager } from '@/lib/api-gateway-handlers/users-handler';
import { getStripeInstance } from '@/lib/stripe';
import { getUserIdFromRequest } from '@/lib/auth/serverAuth';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ paymentIntentId: string }> }
) {
  try {
    const { paymentIntentId } = await params;

    if (!paymentIntentId) {
      return NextResponse.json({ error: 'Payment Intent ID is required' }, { status: 400 });
    }

    console.log('🔍 Retrieving payment intent:', paymentIntentId);

    // Determine Stripe platform by authenticated user's isUS flag (fallback if unauthenticated)
    let stripeClient;
    let isUS: boolean = false;
    let userId: string | null = null;
    try {
      userId = await getUserIdFromRequest(request);
    } catch {}

    if (userId) {
      const resp = await UsersHandlerManager.getInstance().CheckSellerIsUS(userId);
      isUS = Boolean(resp?.isUS);
      stripeClient = getStripeInstance(isUS);
    } else {
      console.log('No authenticated user ID found in request; attempting PI retrieval with US-first fallback');
      // Default to US first when unauthenticated; retrieval below will try the other platform if needed
      isUS = true;
      stripeClient = getStripeInstance(true);
    }

    // Retrieve the payment intent from Stripe; on resource_missing, try the other platform once
    let paymentIntent;
    try {
      paymentIntent = await stripeClient.paymentIntents.retrieve(paymentIntentId);
    } catch (err: any) {
      const statusCode = err?.statusCode;
      const code = err?.code;
      if (code === 'resource_missing' || statusCode === 404) {
        // Try alternate platform
        const altClient = getStripeInstance(!isUS);
        try {
          paymentIntent = await altClient.paymentIntents.retrieve(paymentIntentId);
          stripeClient = altClient;
        } catch (err2) {
          throw err; // original error
        }
      } else {
        throw err;
      }
    }

    console.log('✅ Payment intent retrieved:', {
      id: paymentIntent.id,
      status: paymentIntent.status,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      charge: paymentIntent.latest_charge
    });

    return NextResponse.json({
      success: true,
      paymentIntentId: paymentIntent.id,
      clientSecret: paymentIntent.client_secret,
      status: paymentIntent.status,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      metadata: paymentIntent.metadata,
      charge: paymentIntent.latest_charge
    });

  } catch (error) {
    console.error('❌ Error retrieving payment intent:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ 
      success: false,
      error: errorMessage 
    }, { status: 500 });
  }
}
