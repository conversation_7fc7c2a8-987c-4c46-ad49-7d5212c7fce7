import { UsersHandlerManager } from '@/lib/api-gateway-handlers/users-handler';
import { getUserIdFromRequest } from '@/lib/auth/serverAuth';

import { NextRequest, NextResponse } from 'next/server';
import { Stripe } from 'stripe';
import { getStripeInstance, getStripeInstanceByCurrency } from '@/lib/stripe';
import { OrdersHandlerManager } from '@/lib/api-gateway-handlers/order-handlers';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not defined");
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export async function POST(request: NextRequest) {
  try {
    const {
      // paymentIntentId,
      // chargeId,
      // transactionId,
      cancellationReason,
      currency,
      orderId,
      isUS = false
 ,loggedInUser,sellerName,title,uniqueOrderId,userName,
  description,from,newDueDate,orderInfoStatus,reason
    } = await request.json();
    const orderRes = await OrdersHandlerManager.getInstance().getOrderById(orderId);
    if(!orderRes.success){
  return NextResponse.json({
        success: false,
        error: 'order not found'
      }, { status: 400 });
    }
    const {payment_intent_id:paymentIntentId , transactionId ,chargeId } = orderRes.order;

    if (!paymentIntentId && !chargeId && !transactionId) {
      return NextResponse.json({
        success: false,
        error: 'Payment Intent ID, Charge ID, or Transaction ID is required'
      }, { status: 400 });
    }

    console.log('🚫 ===== CANCELING PAYMENT INTENT =====');
    console.log(`💳 Payment Intent ID: ${paymentIntentId || 'Not provided'}`);
    console.log(`💳 Charge ID: ${chargeId || 'Not provided'}`);
    console.log(`📋 Transaction ID: ${transactionId || 'Not provided'}`);
    console.log(`📝 Cancellation Reason: ${cancellationReason || 'Not specified'}`);
    console.log(`💱 Currency: ${currency || 'Not specified'}`);
    console.log(`🌍 Using US Stripe: ${isUS}`);
    console.log('🕐 Timestamp:', new Date().toISOString());

    // Resolve payment intent ID from different sources
    let resolvedPaymentIntentId = paymentIntentId;
    let resolvedCurrency = currency;

    // If transactionId is provided, treat it as a PaymentIntent ID if it looks like one (pi_*)
    if (!resolvedPaymentIntentId && transactionId) {
      console.log('🔄 Resolving payment intent from provided transactionId...');
      if (typeof transactionId === 'string' && transactionId.startsWith('pi_')) {
        resolvedPaymentIntentId = transactionId;
        console.log(`✅ Using transactionId as PaymentIntent: ${resolvedPaymentIntentId}`);
      } else {
        // Best-effort: try retrieving as a PaymentIntent using default Stripe to infer currency
        try {
          const pi = await stripe.paymentIntents.retrieve(transactionId);
          if (pi?.id) {
            resolvedPaymentIntentId = pi.id;
            resolvedCurrency = resolvedCurrency || pi.currency;
            console.log(`✅ Resolved PaymentIntent by direct lookup: ${resolvedPaymentIntentId}`);
          }
        } catch (e) {
          console.warn('⚠️ Could not resolve transactionId as PaymentIntent; provide paymentIntentId or chargeId.', e);
        }
      }
    }

    // If charge ID is provided, get payment intent from charge
    if (!resolvedPaymentIntentId && chargeId) {
      console.log('🔄 Resolving payment intent from charge ID...');
      try {
        // Use default stripe instance first, will be corrected later if needed
        const charge = await stripe.charges.retrieve(chargeId);
        if (charge.payment_intent) {
          resolvedPaymentIntentId = charge.payment_intent as string;
          resolvedCurrency = resolvedCurrency || charge.currency;
          console.log(`✅ Resolved payment intent from charge: ${resolvedPaymentIntentId}`);
        } else {
          return NextResponse.json({
            success: false,
            error: 'Charge does not have an associated payment intent',
            chargeId
          }, { status: 400 });
        }
      } catch (error) {
        return NextResponse.json({
          success: false,
          error: 'Failed to retrieve charge details',
          details: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 500 });
      }
    }

    if (!resolvedPaymentIntentId) {
      return NextResponse.json({
        success: false,
        error: 'Could not resolve payment intent ID from provided parameters'
      }, { status: 400 });
    }

    // Determine which Stripe instance to use by stored isUS when possible
    let actualIsUS = false;
    try {
      const userId = await getUserIdFromRequest(request as any);
      if (userId) {
        const resp = await UsersHandlerManager.getInstance().CheckSellerIsUS(userId);
        actualIsUS = Boolean(resp?.isUS);
      }
    } catch {}
    let stripeInstance = getStripeInstance(actualIsUS);

    // Fallback to currency inference only if isUS cannot be determined
    if (!actualIsUS && resolvedCurrency) {
      const { stripeInstance: currencyStripe, isUS: currencyIsUS } = getStripeInstanceByCurrency(resolvedCurrency);
      stripeInstance = currencyStripe;
      actualIsUS = currencyIsUS;
      console.log(`🌍 Using ${actualIsUS ? 'US' : 'International'} Stripe instance for ${resolvedCurrency.toUpperCase()} currency`);
    }

    // First, retrieve the payment intent to check its current status
    console.log('🔄 Retrieving payment intent details...');
    const paymentIntent = await stripeInstance.paymentIntents.retrieve(resolvedPaymentIntentId);

    console.log('📊 Payment Intent Details:', {
      id: paymentIntent.id,
      status: paymentIntent.status,
      amount: paymentIntent.amount,
      amountFormatted: `${(paymentIntent.amount / 100).toFixed(2)} ${paymentIntent.currency.toUpperCase()}`,
      currency: paymentIntent.currency,
      capture_method: paymentIntent.capture_method,
      amount_capturable: paymentIntent.amount_capturable,
      latest_charge: paymentIntent.latest_charge
    });

    // Check if the payment intent can be canceled
    const cancelableStatuses = [
      'requires_payment_method',
      'requires_capture',
      'requires_confirmation',
      'requires_action',
      'processing'
    ];

    if (!cancelableStatuses.includes(paymentIntent.status)) {
      return NextResponse.json({
        success: false,
        error: `Cannot cancel payment intent with status '${paymentIntent.status}'. Cancelable statuses are: ${cancelableStatuses.join(', ')}`,
        paymentIntent: {
          id: paymentIntent.id,
          status: paymentIntent.status,
          amount: paymentIntent.amount,
          amountFormatted: `${(paymentIntent.amount / 100).toFixed(2)} ${paymentIntent.currency.toUpperCase()}`,
          currency: paymentIntent.currency
        }
      }, { status: 400 });
    }

    // Check if it's already canceled
    if (paymentIntent.status === 'canceled') {
      return NextResponse.json({
        success: false,
        error: 'Payment intent is already canceled',
        paymentIntent: {
          id: paymentIntent.id,
          status: paymentIntent.status,
          canceled_at: paymentIntent.canceled_at,
          cancellation_reason: paymentIntent.cancellation_reason
        }
      }, { status: 400 });
    }

    // Prepare cancellation parameters
    const cancelParams: any = {};

    if (cancellationReason) {
      // Validate cancellation reason
      const validReasons = ['duplicate', 'fraudulent', 'requested_by_customer', 'abandoned'];
      if (validReasons.includes(cancellationReason)) {
        cancelParams.cancellation_reason = cancellationReason as Stripe.PaymentIntentCancelParams.CancellationReason;
      } else {
        console.log(`⚠️ Invalid cancellation reason '${cancellationReason}'. Valid reasons: ${validReasons.join(', ')}`);
      }
    }
    console.log({cancelParams});

await stripe.paymentIntents.update(resolvedPaymentIntentId, {
  metadata: {
 loggedInUser,sellerName,title,uniqueOrderId,userName,
  description,from,newDueDate,orderInfoStatus,reason
  },
});
    // Cancel the payment intent
    console.log('🔄 Canceling payment intent...');
    const canceledPaymentIntent = await stripeInstance.paymentIntents.cancel(
      resolvedPaymentIntentId,
      cancelParams
    );

    console.log('✅ Payment intent canceled successfully:', {
      id: canceledPaymentIntent.id,
      status: canceledPaymentIntent.status,
      canceled_at: canceledPaymentIntent.canceled_at,
      cancellation_reason: canceledPaymentIntent.cancellation_reason,
      amount: canceledPaymentIntent.amount,
      amountFormatted: `${(canceledPaymentIntent.amount / 100).toFixed(2)} ${canceledPaymentIntent.currency.toUpperCase()}`
    });

    // For requires_capture status, check if amount was automatically refunded
    let refundInfo = null;
    if (paymentIntent.status === 'requires_capture' && paymentIntent.amount_capturable > 0) {
      refundInfo = {
        message: 'Payment was authorized but not captured. The remaining amount_capturable has been automatically refunded.',
        refundedAmount: paymentIntent.amount_capturable,
        refundedAmountFormatted: `${(paymentIntent.amount_capturable / 100).toFixed(2)} ${paymentIntent.currency.toUpperCase()}`
      };
    }

    const responseData = {
      success: true,
      message: 'Payment intent canceled successfully',
      paymentIntent: {
        id: canceledPaymentIntent.id,
        status: canceledPaymentIntent.status,
        originalAmount: canceledPaymentIntent.amount,
        originalAmountFormatted: `${(canceledPaymentIntent.amount / 100).toFixed(2)} ${canceledPaymentIntent.currency.toUpperCase()}`,
        currency: canceledPaymentIntent.currency,
        canceled_at: canceledPaymentIntent.canceled_at,
        cancellation_reason: canceledPaymentIntent.cancellation_reason,
        capture_method: canceledPaymentIntent.capture_method,
        latest_charge: canceledPaymentIntent.latest_charge
      },
      refundInfo,
      resolvedFrom: {
        paymentIntentId: paymentIntentId ? 'direct' : null,
        chargeId: chargeId && !paymentIntentId ? chargeId : null,
        transactionId: transactionId && !paymentIntentId && !chargeId ? transactionId : null,
        resolvedPaymentIntentId: resolvedPaymentIntentId
      },
      stripeInstance: {
        isUS: actualIsUS,
        currency: resolvedCurrency || paymentIntent.currency
      },
      timestamp: new Date().toISOString()
    };

    console.log('✅ ===== CANCELLATION COMPLETED =====');
    console.log(`💳 Payment Intent ID: ${canceledPaymentIntent.id}`);
    console.log(`📊 Status: ${canceledPaymentIntent.status}`);
    console.log(`💰 Original Amount: ${(canceledPaymentIntent.amount / 100).toFixed(2)} ${canceledPaymentIntent.currency.toUpperCase()}`);
    console.log(`📝 Cancellation Reason: ${canceledPaymentIntent.cancellation_reason || 'Not specified'}`);
    console.log('🔚 ===== END CANCELLATION =====');

    return NextResponse.json(responseData);

  } catch (error) {
    console.error('❌ Error canceling payment intent:', error);

    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json({
        success: false,
        error: 'Stripe API error',
        details: error.message,
        type: error.type,
        code: error.code
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: 'Failed to cancel payment intent',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// GET method for convenience
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const paymentIntentId = searchParams.get('payment_intent_id');
    const chargeId = searchParams.get('charge_id');
    const transactionId = searchParams.get('transaction_id');
    const cancellationReason = searchParams.get('cancellation_reason');
    const currency = searchParams.get('currency');
    const isUS = searchParams.get('isUS') === 'true';

    if (!paymentIntentId && !chargeId && !transactionId) {
      return NextResponse.json({
        success: false,
        error: 'Payment Intent ID, Charge ID, or Transaction ID is required'
      }, { status: 400 });
    }

    // Convert to POST request format
    const postRequest = new NextRequest(request.url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        paymentIntentId,
        chargeId,
        transactionId,
        cancellationReason,
        currency,
        isUS
      })
    });

    return POST(postRequest);

  } catch (error) {
    console.error('❌ Error in GET cancel:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to process cancellation request',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
