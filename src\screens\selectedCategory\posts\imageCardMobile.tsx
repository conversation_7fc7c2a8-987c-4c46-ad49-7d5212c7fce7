import sanitizeDStorageUrl from "@/lib/sanatizeUrl";
import { getUserByPostId } from "@/services/usersServices";
import Link from "next/link";
import { useRouter } from "next/navigation";
import LazyMedia from "@/components/LazyMedia";
import { generateFileUrl } from "@/lib/urlGenerator";

const ImageCardMobile = (props: any) => {
  const router = useRouter(); // Ensure it's used on the client
  // const generateFileUrl = (postFile: string | undefined): string | undefined => {
  //   const baseUrl = process.env.BASE_STORAGE_URL;
  //   if (!baseUrl) return undefined;

  //   // Check if postFile is a valid string
  //   if (!postFile) {
  //     return undefined; // Return undefined if postFile is null, undefined, or an empty string
  //   }

  //   // If the postFile already includes the baseUrl, return it as-is
  //   if (postFile.startsWith("https://firebasestorage.googleapis.com/")) {
  //     return postFile;
  //   }

  //   // Otherwise, construct the URL and return it
  //   return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
  // };

  const handleClick = async (category: string, idparam: string) => {
    // here start loading
    try {
      const data = await getUserByPostId(idparam);
      const userId = data?.users?.[0]?.id || "unknown"; // Fallback if no user is found

      router.push(`/browse/${category}/${idparam}%20${userId}`);
    } catch (error) {
      console.error("Error fetching user:", error);
    }
  };
  return (
    <>
      <div
        className="flex flex-row max-md:flex-col w-full overflow-scroll max-md:gap-4 gap-3 hide-scroll bg-white h-full md:hidden"
        // ref={postsRef}
      >
        <div className="w-full mb-[1px]">
          {Array.from({ length: 1 }).map((_, indexs) => (
            <div key={indexs} className="grid grid-cols-5  gap-1 mb-1">
              {Array.from({ length: 5 }).map((_, indexs) => (
                <div className=" mt-0 mr-[1px]" key={indexs}>
                  <div className="">
                    <Link
                      href={`/browse/${props.chunk[indexs]?.category}/${props.chunk[indexs]?.id}`}
                      className="block"
                      onClick={() =>
                        handleClick(
                          props.chunk[indexs]?.category == "Storytelling"
                            ? "Literature"
                            : props.chunk[indexs]?.category,
                          props.chunk[indexs]?.id
                        )
                      }
                    >
                      <div>
                        {props.chunk[indexs]?.mediaType === "image" ? (
                          <LazyMedia
                            src={
                              generateFileUrl(props.chunk[indexs]?.postFile) || "/assets/noimg.png"
                            }
                            alt={`Post ${props.chunkIndex * 5 + 1}`}
                            type="image"
                            className=" h-full w-full object-cover border-2 max-h-[116px] min-h-[116px]"
                            style={{
                              borderColor: props.borderColor,
                            }}
                            placeholderClassName="bg-gray-100"
                          />
                        ) : props.chunk[indexs]?.mediaType === "video" ? (
                          <div className=" relative ">
                            <LazyMedia
                              src={generateFileUrl(props.chunk[indexs]?.postFile)}
                              type="video"
                              className="h-full w-full object-cover border-2 max-h-[116px] min-h-[116px]"
                              style={{
                                borderColor: props.borderColor,
                              }}
                              placeholderClassName="bg-gray-100"
                              showPlayIcon={true}
                              playIconClassName="top-0 right-0"
                              controls={true}
                              autoPlay={false}
                              muted={true}
                            />
                          </div>
                        ) : null}
                      </div>
                    </Link>
                    {indexs == 4 && (
                      <Link
                        href={`/browse/${props.category}/${props?.postId?.id}%20${props.lensId}`}
                        // href="#"
                        className="block"
                      >
                        {props.lensItem1?.image?.item ? (
                          <LazyMedia
                            src={
                              // @ts-ignore
                              sanitizeDStorageUrl(props.lensItem1?.image?.item || "")
                            }
                            alt="Lens content"
                            type="image"
                            className="col-span-4 h-full w-full object-cover border-2 max-h-[116px] min-h-[116px]"
                            style={{ borderColor: props.borderColor }}
                            placeholderClassName="bg-gray-50"
                          />
                        ) : props.lensItem1?.video?.item ? (
                          <LazyMedia
                            src={props.lensItem1?.video?.item}
                            type="video"
                            className="col-span-4 h-full w-full object-cover border-2 max-h-[116px] min-h-[116px]"
                            style={{ borderColor: props.borderColor }}
                            placeholderClassName="bg-gray-50"
                            controls={false}
                            autoPlay={false}
                            muted={true}
                          />
                        ) : null}
                      </Link>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default ImageCardMobile;
