import { NextRequest, NextResponse } from 'next/server';
import { Stripe } from 'stripe';
import { getUserIdFromRequest } from '@/lib/auth/serverAuth';
import { doc, getDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { initFirebase } from '../../../../../firebaseConfig';
import { UsersHandlerManager } from '@/lib/api-gateway-handlers/users-handler';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export async function GET(req: NextRequest) {
  try {
    console.log('GET /api/connect/my-account called');

    // Get user ID from request
    const userId = await getUserIdFromRequest(req);
    console.log('User ID from request:', userId);

    if (!userId) {
      console.log('No authenticated user found');
      return NextResponse.json(
        {
          error: 'Authentication required. Please log in to access your Stripe account details.',
          code: 'AUTH_REQUIRED'
        },
        { status: 401 }
      );
    }

    console.log('Retrieving account details for user:', userId);

    // 1. Get user's stripe_id from users collection
    let accountId;
    try {
      const resp = await UsersHandlerManager.getInstance().CheckUserStripeId(userId);
      if (!resp?.success) {
        return NextResponse.json(
          { error: 'User not found. Please log in again.' },
          { status: 404 }
        );
      }

      if (!resp.hasStripeId || !resp.stripeId) {
        return NextResponse.json(
          { error: 'No connected Stripe account found. Please connect your account first.' },
          { status: 404 }
        );
      }

      accountId = resp.stripeId;

      // Optionally, verify the account exists in stripeAccounts collection
      const { db } = await initFirebase();
      const stripeAccountRef = doc(db, 'stripeAccounts', accountId);
      const stripeAccountDoc = await getDoc(stripeAccountRef);

      if (!stripeAccountDoc.exists()) {
        console.warn(`Stripe account ${accountId} not found in stripeAccounts collection`);
        // Continue anyway as the account might exist in Stripe
      }

    } catch (firebaseError) {
      console.error('Error checking Firebase:', firebaseError);
      return NextResponse.json(
        { error: 'Database error' },
        { status: 500 }
      );
    }

    // 2. Retrieve comprehensive account details from Stripe
    const account = await stripe.accounts.retrieve(accountId, {
      expand: [
        'external_accounts',
        'capabilities',
        'requirements',
        'settings'
      ]
    });

    if (!account || account.object !== 'account') {
      return NextResponse.json(
        { error: 'Account not found in Stripe' },
        { status: 404 }
      );
    }

    // 3. Get recent balance transactions
    let balanceTransactions: any[] = [];
    try {
      // Note: For connected accounts, we need to use the Stripe-Account header
      // which is handled internally by the Stripe SDK when we pass the stripeAccount option
      const transactions = await stripe.balanceTransactions.list(
        { limit: 10 },
        { stripeAccount: accountId }
      );
      balanceTransactions = transactions.data;
    } catch (balanceError) {
      console.log('Could not retrieve balance transactions:', balanceError);
      // Continue without balance data
    }

    // 4. Get account balance
    let balance = null;
    try {
      balance = await stripe.balance.retrieve({
        stripeAccount: accountId
      });
    } catch (balanceError) {
      console.log('Could not retrieve balance:', balanceError);
      // Continue without balance data
    }

    // 5. Format comprehensive account details
    const accountDetails = {
      id: account.id,
      email: account.email,
      type: account.type,
      country: account.country,
      defaultCurrency: account.default_currency,
      created: account.created ? new Date(account.created * 1000).toISOString() : null,
      
      // Business Information
      business: {
        name: account.business_profile?.name,
        url: account.business_profile?.url,
        supportEmail: account.business_profile?.support_email,
        supportPhone: account.business_profile?.support_phone,
        supportUrl: account.business_profile?.support_url,
        productDescription: account.business_profile?.product_description,
        mcc: account.business_profile?.mcc
      },

      // Account Status
      status: {
        detailsSubmitted: account.details_submitted,
        chargesEnabled: account.charges_enabled,
        payoutsEnabled: account.payouts_enabled,
        transfersEnabled: (account as any).transfers_enabled
      },

      // Requirements
      requirements: {
        currentlyDue: account.requirements?.currently_due || [],
        eventuallyDue: account.requirements?.eventually_due || [],
        pastDue: account.requirements?.past_due || [],
        pendingVerification: account.requirements?.pending_verification || [],
        disabledReason: account.requirements?.disabled_reason
      },

      // Capabilities
      capabilities: account.capabilities,

      // Settings
      settings: {
        payouts: account.settings?.payouts,
        payments: account.settings?.payments,
        branding: account.settings?.branding,
        cardPayments: account.settings?.card_payments,
        dashboard: account.settings?.dashboard
      },

      // External Accounts (Bank accounts, cards)
      externalAccounts: account.external_accounts?.data?.map(extAccount => ({
        id: extAccount.id,
        object: extAccount.object,
        currency: extAccount.currency,
        defaultForCurrency: extAccount.default_for_currency,
        ...(extAccount.object === 'bank_account' && {
          bankName: (extAccount as any).bank_name,
          last4: (extAccount as any).last4,
          routingNumber: (extAccount as any).routing_number,
          accountHolderType: (extAccount as any).account_holder_type
        }),
        ...(extAccount.object === 'card' && {
          brand: (extAccount as any).brand,
          last4: (extAccount as any).last4,
          expMonth: (extAccount as any).exp_month,
          expYear: (extAccount as any).exp_year
        })
      })) || [],

      // Balance Information
      balance: balance ? {
        available: balance.available,
        pending: balance.pending,
        connectReserved: balance.connect_reserved,
        instantAvailable: balance.instant_available
      } : null,

      // Recent Transactions
      recentTransactions: balanceTransactions.map(txn => ({
        id: txn.id,
        amount: txn.amount,
        currency: txn.currency,
        description: txn.description,
        fee: txn.fee,
        net: txn.net,
        status: txn.status,
        type: txn.type,
        created: new Date(txn.created * 1000).toISOString()
      }))
    };

    return NextResponse.json({
      success: true,
      account: accountDetails,
      userId
    });

  } catch (error) {
    console.error('Error retrieving account details:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve account details' },
      { status: 500 }
    );
  }
}
