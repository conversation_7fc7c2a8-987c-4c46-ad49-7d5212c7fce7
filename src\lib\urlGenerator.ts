import { initFirebase } from "../../firebaseConfig";
import { getStorage, ref, getDownloadURL } from "firebase/storage";

// Cache to store resolved URLs globally
let urlCache: Record<string, string> = {};
let fileUrlCache: Record<string, string> = {};

/**
 * Improved URL generator that handles most cases synchronously on first load
 * This function tries multiple approaches to generate URLs immediately without async delays
 */
export const generateFileUrl = (postFile: string | undefined): string | undefined => {
  if (!postFile) return undefined;

  // If already cached, return immediately
  if (fileUrlCache[postFile]) {
    return fileUrlCache[postFile];
  }

  // Return a placeholder first (so JSX doesn’t break)
  const placeholder = "";
  fileUrlCache[postFile] = placeholder;

  // Resolve async and cache the real URL
  (async () => {
    if (postFile.includes("https://ik.imagekit.io")) {
      fileUrlCache[postFile] = postFile;
    } else {
      const { app } = await initFirebase();
      const storage = getStorage(app);

      let filePath = postFile.startsWith("https://firebasestorage.googleapis.com/")
        ? decodeURIComponent(postFile.split("/o/")[1].split("?")[0])
        : postFile;

      const fileRef = ref(storage, filePath);
      console.log({ fileRef });

      const url = await getDownloadURL(fileRef);

      fileUrlCache[postFile] = url;
    }

    // Force React re-render (optional, if you use a global store / context / state setter here)
  })();

  return placeholder;
};
/**
 * Clear the URL cache - useful for testing or when you need to refresh URLs
 */
export const clearUrlCache = () => {
  urlCache = {};
};

/**
 * Get the current cache state - useful for debugging
 */
export const getUrlCacheState = () => {
  return { ...urlCache };
};
