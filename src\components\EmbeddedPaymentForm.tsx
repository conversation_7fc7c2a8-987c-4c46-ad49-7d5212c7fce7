"use client";

import React, { useState, useEffect } from 'react';
import { loadStripe, Stripe } from '@stripe/stripe-js';
import {
  Elements,
  PaymentElement,
  PaymentRequestButtonElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { useCurrentUser } from '@/hooks/useCurrentUser';
import { getPublishableKeyByCurrency } from "@/lib/stripeClient";


interface PaymentFormProps {
  clientSecret: string;
  amount: number;
  currency: string;
  productName: string;
  onSuccess?: (paymentIntent: any) => void;
  onError?: (error: string) => void;
}

function CheckoutForm({ clientSecret, amount, currency, productName, onSuccess, onError }: PaymentFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [paymentRequest, setPaymentRequest] = useState<any>(null);
  const [canMakePayment, setCanMakePayment] = useState(false);

  // Initialize Payment Request (Apple Pay/Google Pay)
  useEffect(() => {
    if (!stripe) return;

    // Determine country based on currency
    const getCountryFromCurrency = (curr: string) => {
      const currencyMap: { [key: string]: string } = {
        'usd': 'US',
        'gbp': 'GB',
        'eur': 'DE', // Default to Germany for EUR
        'cad': 'CA',
        'aud': 'AU',
        'jpy': 'JP',
        'chf': 'CH',
        'sek': 'SE',
        'nok': 'NO',
        'dkk': 'DK',
      };
      return currencyMap[curr.toLowerCase()] || 'US';
    };

    const pr = stripe.paymentRequest({
      country: getCountryFromCurrency(currency),
      currency: currency.toLowerCase(),
      total: {
        label: productName,
        amount: amount,
      },
      requestPayerName: true,
      requestPayerEmail: true,
    });

    // Check if Payment Request is available
    pr.canMakePayment().then(result => {
      if (result) {
        setPaymentRequest(pr);
        setCanMakePayment(true);
      }
    });

    // Handle payment method creation
    pr.on('paymentmethod', async (ev) => {
      try {
        const { error, paymentIntent } = await stripe.confirmPayment({
          clientSecret,
          confirmParams: {
            payment_method: ev.paymentMethod.id,
            return_url: `${window.location.origin}/payment-success`,
          },
          redirect: "never"
        });

        if (error) {
          ev.complete('fail');
          setMessage(error.message || "Payment failed");
          onError?.(error.message || "Payment failed");
        } else if (paymentIntent && paymentIntent.status === "succeeded") {
          ev.complete('success');
          setMessage("Payment succeeded!");
          onSuccess?.(paymentIntent);
        }
      } catch (error) {
        ev.complete('fail');
        setMessage("An unexpected error occurred");
        onError?.("An unexpected error occurred");
      }
    });
  }, [stripe, amount, currency, productName, clientSecret, onSuccess, onError]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);

    // Submit the form first (required by Stripe)
    const submitResult = await elements.submit();
    if (submitResult.error) {
      setMessage(submitResult.error.message || "Form validation failed");
      onError?.(submitResult.error.message || "Form validation failed");
      setIsLoading(false);
      return;
    }

    const { error, paymentIntent } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: `${window.location.origin}/payment-success`,
      },
      redirect: "never"
    });

    if (error) {
      if (error.type === "card_error" || error.type === "validation_error") {
        setMessage(error.message || "An error occurred");
        onError?.(error.message || "An error occurred");
      } else {
        setMessage("An unexpected error occurred.");
        onError?.("An unexpected error occurred.");
      }
    } else if (paymentIntent && paymentIntent.status === "succeeded") {
      setMessage("Payment succeeded!");
      onSuccess?.(paymentIntent);
    }

    setIsLoading(false);
  };

  const paymentElementOptions = {
    layout: "tabs" as const,
  };

  return (
    <div>
      {/* Apple Pay / Google Pay Button */}
      {canMakePayment && paymentRequest && (
        <div className="mb-6">
          <div className="payment-request-button">
            <PaymentRequestButtonElement
              options={{
                paymentRequest,
                style: {
                  paymentRequestButton: {
                    type: 'default', // 'default', 'book', 'buy', or 'donate'
                    theme: 'dark', // 'dark', 'light', or 'light-outline'
                    height: '48px',
                  },
                },
              }}
              className="w-full"
            />
          </div>
          <div className="flex items-center my-4">
            <div className="flex-grow border-t border-gray-300"></div>
            <span className="flex-shrink mx-4 text-gray-500 text-sm">or pay with card</span>
            <div className="flex-grow border-t border-gray-300"></div>
          </div>
        </div>
      )}

      {/* Regular Payment Form */}
      <form id="payment-form" onSubmit={handleSubmit}>
        <PaymentElement id="payment-element" options={paymentElementOptions} />
        <button
          disabled={isLoading || !stripe || !elements}
          id="submit"
          className="w-full mt-4 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-3 px-4 rounded"
        >
          <span id="button-text">
            {isLoading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Processing...
              </div>
            ) : (
              "Pay now"
            )}
          </span>
        </button>
        {message && (
          <div className={`mt-4 p-3 rounded ${
            message.includes("succeeded")
              ? "bg-green-100 text-green-700 border border-green-400"
              : "bg-red-100 text-red-700 border border-red-400"
          }`}>
            {message}
          </div>
        )}
      </form>
    </div>
  );
}

interface EmbeddedPaymentFormProps {
  amount: number;
  currency?: string;
  productName?: string;
  userId?: string;
  sellerId?: string;
  orderId?: string;
  isEscrow?: boolean;
  onSuccess?: (paymentIntent: any) => void;
  onError?: (error: string) => void;
}

// Wrapper component that handles clientSecret creation
export default function EmbeddedPaymentForm({
  amount,
  currency,
  productName = "Product",
  userId,
  sellerId,
  orderId,
  isEscrow = false,
  onSuccess,
  onError
}: EmbeddedPaymentFormProps) {
  const { user } = useCurrentUser();
  const [clientSecret, setClientSecret] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Check if we have an existing client secret from URL params
    const urlParams = new URLSearchParams(window.location.search);
    const existingClientSecret = urlParams.get('clientSecret');
    const useExisting = urlParams.get('useExisting') === 'true';

    if (useExisting && existingClientSecret) {
      console.log('Using existing payment intent from URL:', existingClientSecret);
      setClientSecret(existingClientSecret);
      setLoading(false);
      return;
    }

    if (!currency) {
      setError('Currency is required');
      setLoading(false);
      return;
    }

    // Create PaymentIntent as soon as the page loads (only if no existing one)
    const createPaymentIntent = async () => {
      try {
        setLoading(true);
        console.log('Creating NEW payment intent...', { amount, currency, isEscrow });

        const endpoint = isEscrow ? "/api/escrow/create-payment-intent" : "/api/create-payment-intent";

        const response = await fetch(endpoint, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            amount,
            currency,
            productName,
            userId: userId || user?.uid,
            sellerId,
            orderId,
            isEscrow,
            userEmail: user?.email,
            userName: user?.displayName
          }),
        });

        const data = await response.json();
        console.log('Payment intent response:', data);

        if (data.clientSecret) {
          setClientSecret(data.clientSecret);
          console.log('Client secret received:', data.clientSecret);
        } else {
          throw new Error(data.error || "Failed to create payment intent");
        }
      } catch (err) {
        console.error('Error creating payment intent:', err);
        const errorMessage = err instanceof Error ? err.message : "Unknown error";
        setError(errorMessage);
        onError?.(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    createPaymentIntent();
  }, [amount, currency, productName, userId, user?.uid, sellerId, orderId, isEscrow, user?.email, user?.displayName, onError]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading payment form...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded">
        <h3 className="font-semibold">Payment Error</h3>
        <p>{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    );
  }

  if (!clientSecret) {
    return (
      <div className="p-4 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded">
        Waiting for payment setup...
      </div>
    );
  }

  return (
    <PaymentFormWithSecret
      clientSecret={clientSecret}
      amount={amount}
      currency={currency}
      productName={productName}
      isEscrow={isEscrow}
      onSuccess={onSuccess}
      onError={onError}
    />
  );
}

// Separate component that only renders when we have clientSecret
function PaymentFormWithSecret({
  clientSecret,
  amount,
  currency,
  productName,
  isEscrow,
  onSuccess,
  onError
}: {
  clientSecret: string;
  amount: number;
  currency: string;
  productName: string;
  isEscrow: boolean;
  onSuccess?: (paymentIntent: any) => void;
  onError?: (error: string) => void;
}) {
  const appearance = {
    theme: 'stripe' as const,
    variables: {
      colorPrimary: '#2563eb',
      colorBackground: '#ffffff',
      colorText: '#1f2937',
      colorDanger: '#dc2626',
      fontFamily: 'system-ui, sans-serif',
      spacingUnit: '4px',
      borderRadius: '6px',
    },
  };

  const [stripePromise, setStripePromise] = useState<Promise<Stripe | null> | null>(null);

  useEffect(() => {
    const pk = getPublishableKeyByCurrency(currency).publishableKey;
    if (!pk) {
      console.error('Stripe publishable key is not configured');
      return;
    }
    setStripePromise(loadStripe(pk));
  }, [currency]);

  const options = {
    clientSecret,
    appearance,
  };

  return (
    <div className="max-w-md mx-auto">
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">{productName}</h2>
        <p className="text-gray-600">
          Amount: {currency.toUpperCase()} {(amount / 100).toFixed(2)}
          {isEscrow && <span className="ml-2 text-blue-600">(Escrow Payment)</span>}
        </p>
      </div>

      {stripePromise ? (
        <Elements options={options} stripe={stripePromise}>
        <CheckoutForm
          clientSecret={clientSecret}
          amount={amount}
          currency={currency}
          productName={productName}
          onSuccess={onSuccess}
          onError={onError}
        />
      </Elements>
      ) : (
        <div className="p-4 bg-yellow-50 border border-yellow-200 text-yellow-700 rounded">Initializing payment SDK...</div>
      )
    </div>
  );
}
