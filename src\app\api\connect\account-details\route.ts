import { NextRequest, NextResponse } from "next/server";
import { getStripeInstance } from "@/lib/stripe";
import { getUserIdFromRequest } from "@/lib/auth/serverAuth";
import { doc, getDoc } from "firebase/firestore";
import { initFirebase } from "../../../../../firebaseConfig";
import { UsersHandlerManager } from '@/lib/api-gateway-handlers/users-handler';


export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const accountId = searchParams.get("accountId");

    if (!accountId) {
      return NextResponse.json({ error: "Account ID is required" }, { status: 400 });
    }

    // Get user ID from request
    const userId = await getUserIdFromRequest(req);
    if (!userId) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }

    console.log("Retrieving account details for:", accountId, "user:", userId);

    // Select Stripe client based on user's isUS flag
    let isUS = false;
    try {
      const resp = await UsersHandlerManager.getInstance().GetUserById(userId);
      isUS = Boolean(resp?.user?.isUS);
    } catch (e) {
      console.log("Could not resolve user isUS; defaulting to Global");
    }
    const stripeClient = getStripeInstance(isUS);

    // 1. Verify user has access to this account (check Firebase)
    try {
      const { db } = await initFirebase();
      const sellerRef = doc(db, "sellers", userId);
      const sellerDoc = await getDoc(sellerRef);

      if (!sellerDoc.exists() || sellerDoc.data()?.stripeAccountId !== accountId) {
        return NextResponse.json({ error: "Unauthorized access to this account" }, { status: 403 });
      }
    } catch (firebaseError) {
      console.error("Error checking Firebase permissions:", firebaseError);
      return NextResponse.json({ error: "Permission verification failed" }, { status: 500 });
    }

    // 2. Retrieve comprehensive account details from Stripe
    const account = await stripeClient.accounts.retrieve(accountId, {
      expand: ["external_accounts", "capabilities", "requirements", "settings"],
    });

    if (!account || account.object !== "account") {
      return NextResponse.json({ error: "Account not found" }, { status: 404 });
    }

    // 3. Get recent balance transactions
    let balanceTransactions: any[] = [];
    try {
      const transactions = await stripeClient.balanceTransactions.list(
        {
          limit: 10,
        },
        {
          stripeAccount: accountId,
        }
      );
      balanceTransactions = transactions.data;
    } catch (balanceError) {
      console.log("Could not retrieve balance transactions:", balanceError);
      // Continue without balance data
    }

    // 4. Get account balance
    let balance = null;
    try {
      balance = await stripeClient.balance.retrieve({
        stripeAccount: accountId,
      });
    } catch (balanceError) {
      console.log("Could not retrieve balance:", balanceError);
      // Continue without balance data
    }

    // 5. Format comprehensive account details
    const accountDetails = {
      id: account.id,
      email: account.email,
      type: account.type,
      country: account.country,
      defaultCurrency: account.default_currency,
      created: account.created ? new Date(account.created * 1000).toISOString() : null,

      // Business Information
      business: {
        name: account.business_profile?.name,
        url: account.business_profile?.url,
        supportEmail: account.business_profile?.support_email,
        supportPhone: account.business_profile?.support_phone,
        supportUrl: account.business_profile?.support_url,
        productDescription: account.business_profile?.product_description,
        mcc: account.business_profile?.mcc,
      },

      // Account Status
      status: {
        detailsSubmitted: account.details_submitted,
        chargesEnabled: account.charges_enabled,
        payoutsEnabled: account.payouts_enabled,
        transfersEnabled: (account as any).transfers_enabled,
      },

      // Requirements
      requirements: {
        currentlyDue: account.requirements?.currently_due || [],
        eventuallyDue: account.requirements?.eventually_due || [],
        pastDue: account.requirements?.past_due || [],
        pendingVerification: account.requirements?.pending_verification || [],
        disabledReason: account.requirements?.disabled_reason,
      },

      // Capabilities
      capabilities: account.capabilities,

      // Settings
      settings: {
        payouts: account.settings?.payouts,
        payments: account.settings?.payments,
        branding: account.settings?.branding,
        cardPayments: account.settings?.card_payments,
        dashboard: account.settings?.dashboard,
      },

      // External Accounts (Bank accounts, cards)
      externalAccounts:
        account.external_accounts?.data?.map((extAccount) => ({
          id: extAccount.id,
          object: extAccount.object,
          currency: extAccount.currency,
          defaultForCurrency: extAccount.default_for_currency,
          ...(extAccount.object === "bank_account" && {
            bankName: (extAccount as any).bank_name,
            last4: (extAccount as any).last4,
            routingNumber: (extAccount as any).routing_number,
            accountHolderType: (extAccount as any).account_holder_type,
          }),
          ...(extAccount.object === "card" && {
            brand: (extAccount as any).brand,
            last4: (extAccount as any).last4,
            expMonth: (extAccount as any).exp_month,
            expYear: (extAccount as any).exp_year,
          }),
        })) || [],

      // Balance Information
      balance: balance
        ? {
            available: balance.available,
            pending: balance.pending,
            connectReserved: balance.connect_reserved,
            instantAvailable: balance.instant_available,
          }
        : null,

      // Recent Transactions
      recentTransactions: balanceTransactions.map((txn) => ({
        id: txn.id,
        amount: txn.amount,
        currency: txn.currency,
        description: txn.description,
        fee: txn.fee,
        net: txn.net,
        status: txn.status,
        type: txn.type,
        created: new Date(txn.created * 1000).toISOString(),
      })),
    };

    return NextResponse.json({
      success: true,
      account: accountDetails,
      userId,
    });
  } catch (error) {
    console.error("Error retrieving account details:", error);
    return NextResponse.json({ error: "Failed to retrieve account details" }, { status: 500 });
  }
}
