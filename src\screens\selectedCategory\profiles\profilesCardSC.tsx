import { useCallback, useEffect, useRef, useState } from "react";
import { getAllUsers, getUserById } from "@/services/usersServices";
import { themes } from "../../../../theme";
import GlobalProfileCard from "@/globalComponents/globalProfileCard";
import useAuth from "@/hook";
import useProfile from "@/hook/profileData";
import LoadingOverlay from "@/components/loadingOverlay";
import { getLensProfilesById } from "@/services/lensService";
import { useFilter } from "@/context/FilterContext";
import { useProfilesQuery } from "@/graphql/generated";
import GlobalProfileCardLens from "@/globalComponents/globalProfileCardLens";
import { sortProfiles } from "@/lib/helper";
import { FollowerManager } from "@/services/followServices";
import {
  AccountsBulkQuery,
  FollowingOrderBy,
  PageSize,
  useAccountsBulkQuery,
  useFollowingQuery,
} from "@/graphql/test/generated";
import { getLocation } from "@/lib/utils";
import { getId } from "@/services/authBridgeService";

const ProfileCardSC = (props: any) => {
  const user = useAuth();
  const { filters, getServiceFilters } = useFilter();
  // console.log({ user });

  const isAuthLogin = user.isLogin;
  // console.log(isAuthLogin);

  const { profileData } = useProfile(user?.userId || "");
  const [categoryData, setCategoryData] = useState<Record<string, any[]>>({});
  const [profilesData, setProfilesData] = useState<AccountsBulkQuery["accountsBulk"]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  const [followingList, setFollowingList] = useState<any[]>([]);

  const categories = [
    "Music",
    "Literature",
    "Art",
    "Film & Photography",
    "Theatre & Performance",
    "Multidisciplinary",
    "Groups",
    "Storytelling",
  ];

  // Fetch users from API
  const fetchAllUsers = useCallback(async () => {
    try {
      setLoading(true);
      const serviceFilters = getServiceFilters();

      console.log("serviceFilters", serviceFilters);

      // Check if filters are applied
      const filtersApplied = Object.keys(serviceFilters).length > 0;

      // Check if we have meaningful filters beyond just user_id
      const hasValidUserIdFilter = serviceFilters.user_id && serviceFilters.user_id.length > 0;
      const hasOtherValidFilters =
        (serviceFilters.location && serviceFilters.location.length > 0) ||
        serviceFilters.date_of_publishing;

      // Also check the original filters object for profileName
      const hasProfileNameFilter = filters.profileName && filters.profileName.length > 0;

      // Conditional logic for getAllUsers call
      let response;
      if (filtersApplied) {
        // When filters are applied, call getAllUsers if:
        // 1. user_id exists and is not empty, OR
        // 2. there are other valid filters (location, date_of_publishing), OR
        // 3. there's a profileName filter in the original filters
        if (hasValidUserIdFilter) {
          // console.log("Calling getAllUsers with filters:", {
          //   hasValidUserIdFilter,
          //   hasOtherValidFilters,
          //   hasProfileNameFilter,
          //   serviceFilters,
          //   originalFilters: filters,
          // });
          response = await getAllUsers(serviceFilters);
        } else {
          // Skip API call if filters are applied but no meaningful filters exist
          // console.log("Skipping getAllUsers call - filters applied but no meaningful filters:", {
          //   filtersApplied,
          //   hasValidUserIdFilter,
          //   hasOtherValidFilters,
          //   hasProfileNameFilter,
          //   serviceFilters,
          //   originalFilters: filters,
          // });
          response = { users: [] };
        }
      } else {
        // When no filters are applied, always call getAllUsers normally
        console.log("Calling getAllUsers without filters");
        response = await getAllUsers(serviceFilters);
      }

      if (Array.isArray(response?.users) && response.users.length > 0) {
        const categorizedData: Record<string, any[]> = {};
        let myFeed: any[] = [];

        // Process categories efficiently
        for (const category of categories) {
          const filteredPosts = response.users.filter((post: any) => {
            const userCategory = post?.categories?.[0];
            if (!post.profile_name) return false;

            return category === "Literature"
              ? userCategory === category || userCategory === "Storytelling"
              : userCategory === category;
          });

          categorizedData[category] = filteredPosts;
        }

        // Fetch following data only once if user is logged in
        if (user.isLogin && user.userId) {
          try {
            myFeed = await FollowerManager.getInstance().GetFollowingsByUserId(user.userId);
            categorizedData["My Feed"] = myFeed;
          } catch (error) {
            console.error("Error fetching following data:", error);
          }
        }

        setCategoryData(categorizedData);
      }
    } catch (error) {
      console.error("Error fetching posts:", error);
    } finally {
      setLoading(false);
    }
  }, [user.userId, user.isLogin, getServiceFilters, categories]);

  useEffect(() => {
    fetchAllUsers();
  }, [user.userId, filters]);

  // Fetch Lens profiles by category
  const [profiles, setProfiles] = useState<
    Array<{
      localName: string;
    }>
  >([]);
  const [profilesmy, setProfilesMy] = useState<
    Array<{
      localName: string;
    }>
  >([]);
  // const [profilesData, setProfilesData] = useState<any>(null);
  const category = props.themeProperties.title.toLowerCase();
  const profilesRef = useRef<
    Array<{
      localName: string;
    }>
  >([]);

  useEffect(() => {
    const fetchLensProfilesByCategory = async () => {
      const resp = await getLensProfilesById(category);
      const lensProfiles: Array<{
        localName: string;
      }> = resp?.lens_ids?.map((curr: any) => {
        return {
          localName: curr,
        };
      });

      if (JSON.stringify(profilesRef.current) !== JSON.stringify(lensProfiles)) {
        profilesRef.current = lensProfiles;
        setProfiles(lensProfiles);
      }
    };

    fetchLensProfilesByCategory();
  }, [category]);

  // const { data: profileDataByLensId } = useProfilesQuery({
  //   request: { where: { handles: profiles } },
  // });
  const getLensUsernamesForCategory = () => {
    if (!filters.lensProfiles || !Array.isArray(filters.lensProfiles)) {
      return [];
    }

    // Convert display title to lens category format for matching
    // const lensCategoryName = getTitleToLensCategoryMapping(item.title);

    const matchingLensProfile = filters.lensProfiles.find(
      (lensProfile: any) => lensProfile.category === props.themeProperties.title.toLowerCase()
    );

    const usernames = matchingLensProfile ? matchingLensProfile.usernames : [];
    console.log(
      `ProfileHome: Category "${props.themeProperties.title}" (${props.themeProperties.title}) -> usernames:`,
      usernames
    );

    return usernames;
  };

  const hasFilterApplied = Array.isArray(filters?.profileName);
  const {
    data: profileDataByLensId,
    error: profileError,
    isLoading: loadingProfile,
  } = useAccountsBulkQuery(
    {
      request: {
        usernames:
          props.themeProperties.title === "My Feed"
            ? profilesmy
            : filters?.profileName && filters?.profileName?.length > 0
              ? getLensUsernamesForCategory()?.length > 0
                ? getLensUsernamesForCategory().map((name: string) => ({ localName: name }))
                : [{ localName: "" }]
              : hasFilterApplied
                ? [{ localName: "" }]
                : profiles,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled:
        props.themeProperties.title == "My Feed" ? profilesmy.length > 0 : profiles.length > 0,
    }
  );

  useEffect(() => {
    if (profileDataByLensId) {
      setProfilesData(profileDataByLensId?.accountsBulk);
    }
  }, [profileDataByLensId, filters]);

  useEffect(() => {
    if (profilesData) {
      // console.log({ profilesData });
    }
  }, [profilesData]);

  const mergedProfiles = sortProfiles(
    [
      ...(categoryData[props.themeProperties.title] || []).map((item) => ({
        ...item,
        profile_name: item.profile_name || "Profile Name*",
      })),
      ...profilesData.map((profile) => ({
        id: profile.username?.localName,
        profile_name: profile.metadata?.name || "Profile Name*",
        avatar: profile?.metadata?.picture || "",
        location: getLocation(profile.metadata?.attributes) || profile.username?.localName,
        isFollow: profile?.operations?.isFollowedByMe,
        lensProfile: true, // Mark lens profiles
      })),
    ],
    "profile_name"
  );

  // my feed

  const [currentFollowingCursor, setCurrentFollowingCursor] = useState<string | null>(null);
  const [userId, setUserId] = useState("");
  const containerRef = useRef<HTMLDivElement>(null);

  const getLensUserId = async (otherUserID: any) => {
    try {
      const resp = await getId({ id: user.userId });
      if (resp) {
        setUserId(resp?.lens_code);
      }
    } catch (error) {
      console.log({ error });
    }
  };

  useEffect(() => {
    getLensUserId(user.userId);
  }, [user.userId]);
  const { data: following, isLoading: following_loading } = useFollowingQuery(
    {
      request: {
        account: userId, // address
        pageSize: PageSize.Fifty,
        orderBy: FollowingOrderBy.Desc,
        cursor: currentFollowingCursor,
      },
    },
    {
      refetchOnWindowFocus: false,
    }
  );

  interface FollowingItem {
    localName: any;
  }

  const [allFollowing, setAllFollowing] = useState<FollowingItem[]>([]);

  useEffect(() => {
    if (
      following?.following?.items &&
      following?.following?.items.length > 0 &&
      props.themeProperties.title == "My Feed"
    ) {
      const newArray: FollowingItem[] = following.following.items.map((item: any) => ({
        localName: item.following.username?.localName,
      }));

      if (props.themeProperties.title == "My Feed") {
        setAllFollowing((prev) => [...prev, ...newArray]);
      }

      setProfiles((prev) => [...prev, ...newArray]);
    }
  }, [following]);

  useEffect(() => {
    if (allFollowing) {
      setProfilesMy(allFollowing);
    }
  }, [allFollowing]);
  return (
    <>
      {loading ? (
        <LoadingOverlay isLoading={loading} />
      ) : (
        <div className="w-full mt-0">
          {Object.entries(themes).map(([themeName, themeProperties], indexM) => (
            <div key={indexM}>
              {props.themeProperties.title === themeProperties.title && (
                <div className="grid grid-cols-4 max-md:grid-cols-1 max-lg:grid-cols-2 gap-3 mt-4">
                  {(() => {
                    const currentCategory = themeProperties.title;

                    // Check if filters are applied and if current category is in selected categories
                    if (filters.categories && filters.categories.length > 0) {
                      if (!filters.categories.includes(currentCategory)) {
                        // Current category is not in selected filters, show no data
                        return (
                          <div className="col-span-4 w-full mt-2 p-4 bg-gray-50 rounded-md border border-gray-200 text-center">
                            <p className="text-gray-500">No profiles found in this category.</p>
                          </div>
                        );
                      }
                    }

                    return mergedProfiles.length > 0 ? (
                      mergedProfiles.map((post, index) => (
                        <div key={index} className="mt-0">
                          {themeProperties.title === "My Feed" && !post.lensProfile ? (
                            Object.entries(themes).map(([_, innerThemeProperties], idx) => (
                              <div key={idx}>
                                {innerThemeProperties.title ===
                                  (post?.categories[0] === "Storytelling"
                                    ? "Literature"
                                    : post?.categories[0]) && (
                                  <GlobalProfileCard
                                    themeProperties={innerThemeProperties}
                                    isFollow={!profileData?.followers.includes(post.id)}
                                    location={post.location}
                                    profile_name={post.profile_name}
                                    avatar={post.avatar}
                                    id={post.id}
                                  />
                                  // <p>{post.profile_name}hii</p>
                                )}
                              </div>
                            ))
                          ) : themeProperties.title === "My Feed" && post.lensProfile ? (
                            <div>
                              <GlobalProfileCardLens
                                themeProperties={themeProperties}
                                isFollow={post.isFollow}
                                location={post.location}
                                profile_name={post.profile_name}
                                avatar={post.avatar}
                                id={post.id}
                              />
                            </div>
                          ) : post.lensProfile ? (
                            <GlobalProfileCardLens
                              themeProperties={themeProperties}
                              isFollow={post.isFollow}
                              location={post.location}
                              profile_name={post.profile_name}
                              avatar={post.avatar}
                              id={post.id}
                            />
                          ) : (
                            // <p>{post.profile_name} lens</p>
                            <GlobalProfileCard
                              themeProperties={themeProperties}
                              isFollow={!profileData?.followers.includes(post.id)}
                              location={post.location}
                              profile_name={post.profile_name}
                              avatar={post.avatar}
                              id={post.id}
                            />
                            // <p>{post.profile_name}hiii</p>
                          )}
                        </div>
                      ))
                    ) : (
                      <p className="text-gray-500">No Profile available in this category.</p>
                    );
                  })()}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export default ProfileCardSC;
