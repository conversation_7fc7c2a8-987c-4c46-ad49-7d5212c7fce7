import { UsersHandlerManager } from '@/lib/api-gateway-handlers/users-handler';
import { getUserIdFromRequest } from '@/lib/auth/serverAuth';

import { NextRequest, NextResponse } from 'next/server';
import { getStripeInstance } from '@/lib/stripe';
import { verifyAuth } from '@/lib/authMiddleware';

// Auth: require a logged-in user via Bear<PERSON> token from client (Firebase access token)
// No server-side STRIPE_UPDATE_API_TOKEN check.
function unauthorized(message: string = 'Unauthorized') {
  return NextResponse.json({ success: false, error: message }, { status: 401 });
}

function verifyLoggedIn(req: NextRequest) {
  const authHeader = req.headers.get('authorization') || req.headers.get('Authorization');
  if (!authHeader) {
    return unauthorized('Missing Authorization header');
  }
  if (!authHeader.toLowerCase().startsWith('bearer ')) {
    return unauthorized('Missing Bearer token');
  }
  const token = authHeader.slice(7).trim();
  if (!token || token.length < 10) {
    return unauthorized('Invalid Bearer token');
  }
  // Optionally: verify Firebase ID token here using firebase-admin if available.
  return null as NextResponse | null;
}

export async function POST(req: NextRequest) {
     const auth = await verifyAuth(req);
      if ("status" in auth) return auth;
  try {
    const body = await req.json();

    const {
      paymentIntentId,
      description,
      metadata,
      receipt_email,
      // Choose Stripe instance
      isUS,
      currency,
      // Behavior
      mergeMetadata = true,
      // Convenience fields we commonly need
      orderId,
      orderUniqueId,
      // Redirect behavior
      redirect,
      returnUrl,
    } = body || {};

    if (!paymentIntentId) {
      return NextResponse.json(
        { success: false, error: 'paymentIntentId is required' },
        { status: 400 }
      );
    }

    // Select Stripe instance by stored isUS (prefer server-derived over client hints)
    let isUSFlag = false;
    try {
      const userId = await getUserIdFromRequest(req as any);
      if (userId) {
        const resp = await UsersHandlerManager.getInstance().CheckSellerIsUS(userId);
        isUSFlag = Boolean(resp?.isUS);
      }
    } catch {}
    if (!isUSFlag && (isUS === true || isUS === 'true')) {
      isUSFlag = true;
    }
    const stripeClient = getStripeInstance(isUSFlag);

    // Start with current metadata if merge requested
    let baseMetadata: Record<string, string> = {};
    if (mergeMetadata) {
      try {
        const current = await stripeClient.paymentIntents.retrieve(paymentIntentId);
        baseMetadata = { ...(current.metadata as Record<string, string>) };
      } catch {
        // ignore fetch errors and proceed without merging
      }
    }

    // Normalize provided metadata to strings
    const providedMetadata: Record<string, string> | undefined =
      metadata && typeof metadata === 'object'
        ? Object.fromEntries(
            Object.entries(metadata).map(([k, v]) => [k, v == null ? '' : String(v)])
          )
        : undefined;

    const convenience: Record<string, string> = {};
    if (orderId) convenience.orderId = String(orderId);
    if (orderUniqueId) convenience.orderUniqueId = String(orderUniqueId);

    const updateParams: any = {};
    if (description) updateParams.description = String(description);
    if (receipt_email) updateParams.receipt_email = String(receipt_email);

    const newMetadata = { ...baseMetadata, ...(providedMetadata || {}), ...convenience };
    if (Object.keys(newMetadata).length > 0) {
      updateParams.metadata = newMetadata;
    }

    if (Object.keys(updateParams).length === 0) {
      return NextResponse.json(
        {
          success: false,
          error:
            'Nothing to update. Provide description, metadata, receipt_email, orderId, or orderUniqueId.',
        },
        { status: 400 }
      );
    }

    const updated = await stripeClient.paymentIntents.update(
      paymentIntentId,
      updateParams
    );

    // Conditional redirect behavior (best approach: opt-in from caller)
    if (redirect) {
      const target = returnUrl || `${req.nextUrl.origin}/payment`;
      return NextResponse.redirect(target);
    }

    return NextResponse.json({
      success: true,
      paymentIntentId: updated.id,
      description: updated.description,
      metadata: updated.metadata,
      currency: updated.currency,
      status: updated.status,
      nextUrl: `${req.nextUrl.origin}/payment`,
    });
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error updating PaymentIntent:', message);
    return NextResponse.json({ success: false, error: message }, { status: 500 });
  }
}


// export async function GET(req: NextRequest) {
//   const notAuth = verifyBearer(req);
//   if (notAuth) return notAuth;
//   return NextResponse.json({ success: true, message: 'Token valid' });
// }
